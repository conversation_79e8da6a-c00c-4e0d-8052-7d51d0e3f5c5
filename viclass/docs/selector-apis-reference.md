# Selector APIs Reference

## Tổng quan

Document này cung cấp reference đầy đủ cho tất cả APIs trong Selector System của VIClass Geometry Editor.

## Core Selector Classes

### ElementSelector<T>

Base class cho tất cả selectors.

**Properties:**

- `selected?: T` - Element đã được chọn
- `multiple: boolean` - <PERSON><PERSON> cho phép chọn nhiều elements
- `isAccepted: boolean` - Trạng thái đã accept selection
- `curGeneratedId: number` - ID cho preview elements

**Methods:**

- `trySelect(event, doc): T | undefined` - Thử chọn element
- `tryHit(event, doc): T | undefined` - <PERSON><PERSON><PERSON> tra hit existing elements
- `tryPreview(event, doc): T | undefined` - Tạo/kiểm tra preview elements
- `reset(keepPreview?: boolean)` - Reset selector state
- `setOption<K>(key: K, value: SelectorOptions[K])` - Set option
- `getOption<K>(key: K): SelectorOptions[K]` - Get option
- `clearTrial(doc)` - Clear preview elements

### VertexSelector

Selector cho việc chọn điểm (vertices).

**Factory Function:**

```typescript
vertex(options?: Partial<VertexSelectorOptions>): VertexSelector
```

**Options:**

- `preview: boolean` - Cho phép chọn preview elements
- `renderEl: boolean` - Cho phép chọn render elements
- `genPreview: boolean` - Tự động tạo preview
- `stickyOnMatch: boolean` - Element dính khi match
- `stickyPTypes: string[]` - Pointer types cho sticky
- `autoAcceptOnPointerUp: boolean` - Auto accept khi pointer up
- `highlightOnMatch: boolean` - Highlight khi match
- `stopSnap: boolean` - Tắt snap to grid
- `tfunc: (previewEl, doc) => RenderVertex` - Transform function
- `cfunc: (el, doc) => boolean` - Check function
- `refinedFilter: (el) => boolean` - Filter function

### StrokeSelector

Selector cho việc chọn strokes (đường, hình).

**Factory Function:**

```typescript
stroke(options?: Partial<StrokeSelectorOptions>): StrokeSelector
```

**Options:**

- `selectableStrokeTypes: GeoRelType[]` - Loại strokes có thể chọn
- `cfunc: (el, doc) => boolean` - Check function
- `highlightOnMatch: boolean` - Highlight khi match
- `autoAcceptOnPointerUp: boolean` - Auto accept

**Selectable Stroke Types:**

- `'RenderLine'` - Đường thẳng vô hạn
- `'RenderLineSegment'` - Đoạn thẳng
- `'RenderVector'` - Vector
- `'RenderRay'` - Tia
- `'RenderCircle'` - Đường tròn
- `'RenderEllipse'` - Ellipse
- `'RenderSector'` - Cung tròn

### VertexOnStrokeSelector

Selector đặc biệt cho việc chọn điểm trên stroke.

**Factory Function:**

```typescript
vertexOnStroke(options?: Partial<VertexOnStrokeSelectorOptions>): VertexOnStrokeSelector
```

**Return Type:** `VertexOnStroke = [StrokeType, RenderVertex]`

**Additional Options:**

- `tfunc: (stroke, vertex, doc) => RenderVertex` - Transform function
- `showPosHint: boolean` - Hiển thị position hint

## Selection DSL

### OrSelector

Chọn một trong nhiều loại elements.

**Factory Function:**

```typescript
or<T>(selectors: ElementSelector[], options?: OrSelectorOptions): OrSelector<T>
```

**Options:**

- `flatten: boolean | number` - Flatten kết quả
- `onComplete: (selector, doc) => void` - Callback khi hoàn thành
- `onReset: (selector) => void` - Callback khi reset

### ThenSelector

Chọn các elements theo thứ tự.

**Factory Function:**

```typescript
then(selectors: ElementSelector[], options?: MultiSelectorOptions): ThenSelector
```

**Options:**

- `flatten: boolean | number` - Flatten kết quả
- `onComplete: (selector, doc) => void` - Callback khi hoàn thành
- `onPartialSelection: (newSel, curSel, selector, doc) => boolean` - Callback cho partial selection

### RepeatSelector

Chọn cùng loại element nhiều lần.

**Factory Function:**

```typescript
repeat<T>(selector: ElementSelector<T>, options: RepeatSelectorOptions): RepeatSelector<T>
```

**Options:**

- `count?: number` - Số lượng elements cần chọn
- `onPartialSelection: (newSel, curSel, selector, doc) => boolean` - Callback cho mỗi selection
- `onComplete: (selector, doc) => void` - Callback khi hoàn thành

## Common Selection Utilities

### vertexS()

Tạo OrSelector thông dụng cho việc chọn vertex.

```typescript
vertexS(q: PreviewQueue, cursor: BehaviorSubject<Cursor[]>): OrSelector<SelectedVertex>
```

**Return:** OrSelector của `vertex()` và `vertexOnStroke()`

### strokeS()

Tạo OrSelector thông dụng cho việc chọn stroke.

```typescript
strokeS(q: PreviewQueue, cursor: BehaviorSubject<Cursor[]>): OrSelector<SelectedStroke>
```

### nPoints()

Chọn n điểm với tự động exclusion.

```typescript
nPoints(
    q: PreviewQueue,
    cursor: BehaviorSubject<Cursor[]>,
    options?: RepeatSelectorOptions
): RepeatSelector<SelectedVertex>
```

### nLines()

Chọn n đường với tự động exclusion.

```typescript
nLines(
    q: PreviewQueue,
    cursor: BehaviorSubject<Cursor[]>,
    options?: RepeatSelectorOptions
): RepeatSelector<SelectedStroke>
```

### triangleWithProj()

Logic chọn tam giác với ràng buộc cho điểm cuối.

```typescript
triangleWithProj(
    pQ: PreviewQueue,
    cursor: BehaviorSubject<Cursor[]>,
    projFunc: (f2p: SelectedVertex[], el: RenderVertex) => RenderVertex,
    checkFunc: (f2p: SelectedVertex[], el: RenderVertex) => boolean,
    thenOptions?: MultiSelectorOptions
): ThenSelector
```

## Transform Functions

### Geometric Constraints

- `perpLinesTransform(f2p, el)` - Ràng buộc góc vuông
- `halfCircleTransform(f2p, el)` - Ràng buộc trên nửa đường tròn
- `equilateralTransform(f2p, el)` - Ràng buộc tam giác đều
- `perpBisectorTransform(f2p, el)` - Ràng buộc trên đường trung trực
- `circleTransform(f2p, el, cIdx?)` - Ràng buộc trên đường tròn

### Check Functions

- `perpLinesCheck(f2p, el)` - Kiểm tra góc vuông
- `halfCircleCheck(f2p, el)` - Kiểm tra trên nửa đường tròn
- `equilateralCheck(f2p, el)` - Kiểm tra tam giác đều
- `perpBisectorCheck(f2p, el)` - Kiểm tra trên đường trung trực
- `circleCheck(f2p, el)` - Kiểm tra trên đường tròn

## Utility Functions

### Type Extraction

```typescript
vert(s: SelectedVertex | RenderVertex): RenderVertex
strk(s: SelectedStroke | StrokeType): StrokeType
```

### Conversion

```typescript
pointsFromSelected(verts: (SelectedVertex | RenderVertex)[]): Point[]
closerPoint(f2p: SelectedVertex[], el: RenderVertex): number
```

### Flattening

```typescript
flatten<T>(selection: SelectableType[], result: T, level: number | boolean): void
```

## Types

### Core Types

```typescript
type SelectableType = GeoRenderElement | GeoRenderElement[] | SelectableType[] | 'nothing';
type SelectedVertex = [RenderVertex] | VertexOnStroke;
type SelectedStroke = [StrokeType];
type VertexOnStroke = [StrokeType, RenderVertex];
```

### Option Types

```typescript
interface SelectorOptions<T> {
    name?: string;
    stickyOnMatch?: boolean;
    autoAcceptOnPointerUp?: boolean;
    highlightOnMatch?: boolean;
    preview?: boolean;
    renderEl?: boolean;
    genPreview?: boolean;
    syncPreview?: boolean;
    previewQueue?: PreviewQueue;
    stickyPTypes?: string[] | 'all';
    cursor?: BehaviorSubject<Cursor[]>;
    refinedFilter?: (el: GeoRenderElement) => boolean;
    onComplete?: (selector: ElementSelector<T>, doc: GeoDocCtrl) => void;
    onReset?: (selector: ElementSelector<T>) => void;
}
```

## Constants

### Preview IDs

```typescript
const PreviewIds = {
    stroke: -1000000,
    vertex: -10000000,
};
```

### Default Selectable Stroke Types

```typescript
const defaultSelectableStrokeTypes = [
    'RenderLine',
    'RenderLineSegment',
    'RenderVector',
    'RenderRay',
    'RenderSector',
    'RenderEllipse',
    'RenderCircle',
];
```

## Error Handling

### Common Patterns

```typescript
// Kiểm tra selection validity
if (!selector.selected || selector.selected.length === 0) {
    this.showError('Không có element nào được chọn');
    return;
}

// Validate selection
try {
    const result = this.processSelection(selector.selected);
    if (!result) {
        throw new Error('Xử lý selection thất bại');
    }
} catch (error) {
    this.showError(`Lỗi: ${error.message}`);
    this.resetState();
}
```

### Reset Patterns

```typescript
override resetState() {
    this.selLogic?.reset();
    this.previewElements = [];
    this.pQ = new PreviewQueue();
    super.resetState();
}
```

## Alternatives to Selector System

### PotentialSelectionDelegator

Cho tools cần basic selection mà không cần DSL phức tạp:

```typescript
private potentialSelectionDelegator = new PotentialSelectionDelegator<MyTool>(this);

// Sử dụng trong handlePointerEvent
this.potentialSelectionDelegator.handlePointerEvent(event);
```

### Direct Hit Detection

Cho tools cần control hoàn toàn logic selection:

```typescript
const hitCtx = ctrl.editor.checkHitInternal(
    ctrl.layers[0],
    event,
    includeSelected: boolean = false,
    includePreview: boolean = false
);

if (hitCtx?.hitDetails?.el) {
    const element = hitCtx.hitDetails.el;
    // Xử lý element
}
```

### Filter Functions

Thay thế cho refinedFilter trong selector:

```typescript
protected filterElementFunc = (el: GeoRenderElement): boolean => {
    // Custom filtering logic
    return el.type === 'RenderVertex' && this.isValidElement(el);
};

// Set filter trước khi hit detection
ctrl.editor.filterElementFunc = this.filterElementFunc;
const hitCtx = ctrl.editor.checkHitInternal(ctrl.layers[0], event);
```

### Event-driven Tools

Cho tools không cần pointer selection:

```typescript
export class UITool extends GeometryTool {
    viewportContentEventListener = new ViewportContentEventListener(this);

    protected async processChangeToolEvent(event: GeoToolEventData) {
        // Xử lý UI events thay vì pointer events
    }
}
```

## Tool Categories

### 1. Selector-based Tools

- **CreateLineTool**: `repeat(vertexS(), {count: 2})`
- **CreateTriangleTool**: `triangleWithProj()`
- **IntersectionPointTool**: `repeat(stroke(), {count: 2})`
- **PointOnObjectTool**: `vertexOnStroke()`
- **CreateAngleTool**: `then([nLines(), vertex()])`
- **CreateBisectorLineTool**: `then([stroke(), vertex()])`
- **CreateSectorTool**: `then([nPoints(), or([vertex(), vertexOnStroke()])])`
- **MiddlePointTool**: `or([repeat(vertex()), vertexOnStroke()])`
- **CreateParallelLineTool**: `createLineToolSelLogic()` utility
- **CreatePerpendicularLineTool**: `createLineToolSelLogic()` utility
- **CreateAngleByThreePointsTool**: `then([nPoints(), vertex()])`
- **CreateCircleTool**: `repeat(vertexS(), {count: 2})`

### 2. Manual Selection Tools

- **CreateTrapezoidTool**: PotentialSelectionDelegator + manual logic
- **CreateIsoscelesRightTriangleTool**: Multiple preview points
- **MoveElementTool**: Threshold detection + constraints
- **CreateSquareTool**: PotentialSelectionDelegator + multiple preview points
- **CreateSymmetricThroughPointTool**: Multi-stage selection + preview management

### 3. UI-driven Tools

- **RenameElementTool**: ViewportContentEventListener
- **ListElementTool**: Action-based processing
- **UpdatePropTool**: Property updates

## Performance Tips

1. **Cache Transform Calculations**: Lưu cache các tính toán phức tạp trong transform functions
2. **Use refinedFilter Efficiently**: Tối ưu refinedFilter để tránh tính toán không cần thiết
3. **Batch Preview Updates**: Sử dụng PreviewQueue để batch update previews
4. **Reset Properly**: Luôn reset state đúng cách để tránh memory leaks
5. **Limit Preview Elements**: Giới hạn số lượng preview elements để tránh performance issues
6. **Choose Right Approach**: Sử dụng Selector khi cần, manual selection khi phù hợp hơn

## Decision Matrix

| Criteria                   | Use Selector | Use Manual | Use UI-driven |
| -------------------------- | ------------ | ---------- | ------------- |
| Canvas selection needed    | ✅           | ✅         | ❌            |
| Complex selection logic    | ✅           | ❌         | ❌            |
| Preview elements           | ✅           | Manual     | ❌            |
| Transform/Check functions  | ✅           | Manual     | ❌            |
| Performance critical       | ❌           | ✅         | ✅            |
| Special selection behavior | ❌           | ✅         | ❌            |
| UI-only interactions       | ❌           | ❌         | ✅            |

Selector APIs cung cấp một framework hoàn chỉnh và mạnh mẽ để xây dựng các geometry tools với logic selection phức tạp và hiệu suất cao. Tuy nhiên, việc lựa chọn approach phù hợp cho từng use case cụ thể là rất quan trọng.
