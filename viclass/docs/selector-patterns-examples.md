# Selector Patterns và Use Cases

## Tổng quan

Document này mô tả các patterns phổ biến và use cases thực tế khi sử dụng Selector APIs trong VIClass Geometry Editor.

## Patterns cơ bản

### Pattern 1: Simple Point Selection

**Use case**: Tạo điểm đơn giản, chọn điểm có sẵn hoặc tạo mới.

```typescript
export class CreatePointTool extends GeometryTool {
    selLogic: VertexSelector;

    constructor() {
        this.selLogic = vertex({
            preview: true, // Cho phép tạo điểm mới
            renderEl: true, // Cho phép chọn điểm có sẵn
            onComplete: this.createPoint.bind(this),
        });
    }
}
```

### Pattern 2: Two Points Line Creation

**Use case**: Tạo đường thẳng từ 2 điểm.

```typescript
export class CreateLineTool extends GeometryTool {
    selLogic: RepeatSelector<SelectedVertex>;

    constructor() {
        this.selLogic = repeat(vertexS(this.pQ, this.cursor), {
            count: 2,
            onComplete: this.createLine.bind(this),
        });
    }

    handlePointerEvent(event: GeoPointerEvent) {
        const selected = this.selLogic.trySelect(event, docCtrl);

        // Hiển thị preview line khi có 2 điểm
        if (selected && selected.length === 2) {
            this.showPreviewLine(vert(selected[0]), vert(selected[1]));
        }
    }
}
```

### Pattern 3: Point on Object

**Use case**: Tạo điểm trên đối tượng khác (đường, đường tròn).

```typescript
export class PointOnObjectTool extends GeometryTool {
    selLogic: OrSelector<SelectedVertex>;

    constructor() {
        this.selLogic = or(
            [
                vertex({ preview: true }), // Điểm tự do
                vertexOnStroke({
                    // Điểm trên stroke
                    selectableStrokeTypes: ['RenderLine', 'RenderCircle'],
                    syncPreview: true,
                }),
            ],
            {
                flatten: true,
                onComplete: this.createPoint.bind(this),
            }
        );
    }
}
```

## Patterns phức tạp

### Pattern 4: Sequential Selection

**Use case**: Chọn các phần tử theo thứ tự cụ thể.

```typescript
export class PerpendicularLineTool extends GeometryTool {
    selLogic: ThenSelector;

    createSelLogic() {
        const lineSelector = stroke({
            selectableStrokeTypes: ['RenderLine'],
            name: 'baseLine',
        });

        const pointSelector = vertexS(this.pQ, this.cursor);
        pointSelector.get('vertex').setOption('name', 'throughPoint');

        this.selLogic = then([lineSelector, pointSelector], {
            onComplete: this.createPerpendicularLine.bind(this),
        });
    }

    createPerpendicularLine(selector: ThenSelector, doc: GeoDocCtrl) {
        const [baseLine, throughPoint] = selector.selected;
        // Tạo đường vuông góc
        this.constructPerpendicularLine(strk(baseLine), vert(throughPoint));
    }
}
```

### Pattern 5: Constrained Selection

**Use case**: Chọn với ràng buộc hình học.

```typescript
export class RightTriangleTool extends GeometryTool {
    selLogic: ThenSelector;

    createSelLogic() {
        // Chọn 2 điểm đầu tự do
        const first2Points = nPoints(this.pQ, this.cursor, { count: 2 });

        // Điểm thứ 3 bị ràng buộc tạo góc vuông
        const thirdPoint = vertex({
            preview: true,
            tfunc: (previewEl, doc) => {
                const selectedPoints = this.selLogic.selected[0] as SelectedVertex[];
                return perpLinesTransform(selectedPoints, previewEl);
            },
            cfunc: (el, doc) => {
                const selectedPoints = this.selLogic.selected[0] as SelectedVertex[];
                return perpLinesCheck(selectedPoints, el);
            },
        });

        this.selLogic = then([first2Points, thirdPoint], {
            onComplete: this.createRightTriangle.bind(this),
        });
    }
}
```

### Pattern 6: Multi-mode Selection

**Use case**: Tool có nhiều chế độ selection khác nhau.

```typescript
export class AngleTool extends GeometryTool {
    selLogic: OrSelector;

    createSelLogic() {
        // Chế độ 1: Chọn góc có sẵn
        const existingAngleSelector = stroke({
            selectableStrokeTypes: ['RenderAngle'],
            name: 'existingAngle',
        });

        // Chế độ 2: Chọn 2 đường thẳng + điểm chỉ hướng
        const lineBasedSelector = then([
            repeat(
                stroke({
                    selectableStrokeTypes: ['RenderLine'],
                    name: 'line',
                }),
                { count: 2 }
            ),
            vertex({ name: 'directionPoint' }),
        ]);

        // Chế độ 3: Chọn 3 điểm
        const pointBasedSelector = nPoints(this.pQ, this.cursor, {
            count: 3,
            name: 'threePoints',
        });

        this.selLogic = or([existingAngleSelector, lineBasedSelector, pointBasedSelector], {
            onComplete: this.createAngle.bind(this),
        });
    }

    createAngle(selector: OrSelector, doc: GeoDocCtrl) {
        const selected = selector.selected;

        // Xử lý theo loại selection
        if (this.isExistingAngle(selected)) {
            this.processExistingAngle(selected);
        } else if (this.isLineBased(selected)) {
            this.processLineBased(selected);
        } else {
            this.processPointBased(selected);
        }
    }
}
```

## Use Cases thực tế

### Use Case 1: Polygon Tool

**Mô tả**: Tạo đa giác với số điểm không giới hạn, kết thúc bằng double-click hoặc click vào điểm đầu.

```typescript
export class PolygonTool extends GeometryTool {
    selLogic: RepeatSelector<SelectedVertex>;
    selectedPoints: RenderVertex[] = [];

    constructor() {
        this.selLogic = repeat(vertexS(this.pQ, this.cursor), {
            onPartialSelection: this.onPointSelected.bind(this),
            // Không set count để cho phép chọn không giới hạn
        });
    }

    onPointSelected(
        newSel: SelectedVertex,
        curSel: SelectedVertex[],
        selector: RepeatSelector<SelectedVertex>,
        doc: GeoDocCtrl
    ): boolean {
        const newPoint = vert(newSel);
        this.selectedPoints.push(newPoint);

        // Kiểm tra kết thúc polygon
        if (this.selectedPoints.length >= 3) {
            // Nếu click vào điểm đầu tiên -> kết thúc
            if (this.isClickOnFirstPoint(newPoint)) {
                this.completePolygon();
                return false; // Dừng repeat
            }
        }

        // Hiển thị preview edges
        this.updatePreviewPolygon();
        return true; // Tiếp tục chọn
    }

    handleDoubleClick(event: GeoPointerEvent) {
        if (this.selectedPoints.length >= 3) {
            this.completePolygon();
        }
    }
}
```

### Use Case 2: Circle Tool với nhiều chế độ

**Mô tả**: Tạo đường tròn bằng center-radius, 2 điểm, hoặc 3 điểm.

```typescript
export class CircleTool extends GeometryTool {
    mode: 'center-radius' | 'two-points' | 'three-points' = 'center-radius';
    selLogic: OrSelector;

    createSelLogic() {
        // Chế độ center-radius: chọn tâm rồi chọn điểm trên đường tròn
        const centerRadiusSelector = then([
            vertexS(this.pQ, this.cursor), // Tâm
            vertexS(this.pQ, this.cursor), // Điểm trên đường tròn
        ]);

        // Chế độ 2 điểm: đường tròn có đường kính là đoạn thẳng nối 2 điểm
        const twoPointsSelector = nPoints(this.pQ, this.cursor, { count: 2 });

        // Chế độ 3 điểm: đường tròn đi qua 3 điểm
        const threePointsSelector = nPoints(this.pQ, this.cursor, { count: 3 });

        this.selLogic = or([centerRadiusSelector, twoPointsSelector, threePointsSelector], {
            onComplete: this.createCircle.bind(this),
        });
    }

    createCircle(selector: OrSelector, doc: GeoDocCtrl) {
        const selected = selector.selected;

        if (selected.length === 2 && this.mode === 'center-radius') {
            this.createCenterRadiusCircle(selected);
        } else if (selected.length === 2 && this.mode === 'two-points') {
            this.createDiameterCircle(selected);
        } else if (selected.length === 3) {
            this.createThreePointCircle(selected);
        }
    }
}
```

### Use Case 3: Intersection Tool

**Mô tả**: Tìm giao điểm của 2 đối tượng.

```typescript
export class IntersectionTool extends GeometryTool {
    selLogic: RepeatSelector<SelectedStroke>;

    constructor() {
        this.selLogic = repeat(strokeS(this.pQ, this.cursor), {
            count: 2,
            onComplete: this.findIntersections.bind(this),
        });
    }

    findIntersections(selector: RepeatSelector<SelectedStroke>, doc: GeoDocCtrl) {
        const [stroke1, stroke2] = selector.selected.map(s => strk(s));

        // Tính toán giao điểm
        const intersections = this.calculateIntersections(stroke1, stroke2);

        if (intersections.length === 0) {
            this.showMessage('Không có giao điểm');
            return;
        }

        // Tạo điểm giao
        intersections.forEach(point => {
            this.createIntersectionPoint(point, stroke1, stroke2);
        });
    }
}
```

### Use Case 4: Symmetry Tool

**Mô tả**: Tạo đối xứng qua điểm hoặc đường thẳng.

```typescript
export class SymmetryTool extends GeometryTool {
    mode: 'point' | 'line' = 'point';
    selLogic: ThenSelector;

    createSelLogic() {
        // Chọn đối tượng cần đối xứng
        const objectSelector = or([vertexS(this.pQ, this.cursor), strokeS(this.pQ, this.cursor)], { flatten: true });

        // Chọn trục đối xứng
        const axisSelector =
            this.mode === 'point'
                ? vertexS(this.pQ, this.cursor) // Điểm đối xứng
                : strokeS(this.pQ, this.cursor); // Đường đối xứng

        this.selLogic = then([objectSelector, axisSelector], {
            onComplete: this.createSymmetry.bind(this),
        });
    }

    createSymmetry(selector: ThenSelector, doc: GeoDocCtrl) {
        const [object, axis] = selector.selected;

        if (this.mode === 'point') {
            this.createPointSymmetry(object, vert(axis));
        } else {
            this.createLineSymmetry(object, strk(axis));
        }
    }
}
```

## Advanced Patterns

### Pattern 7: Ellipse Tool với 2 chế độ

**Use case**: Tool tạo ellipse với 2 chế độ khác nhau - focus points và center vectors.

```typescript
export class CreateEllipseTool extends GeometryTool {
    mode: number = 0; // 0: focus points, 1: center vectors
    selLogic: ThenSelector;

    createSelLogic() {
        // Cả 2 chế độ đều chọn 2 điểm đầu + 1 điểm cuối
        const first2Points = nPoints(this.pQ, this.cursor, { count: 2 });

        const lastVertex = vertex({
            name: this.mode === 0 ? 'pointOnEllipse' : 'vectorBPoint',
            previewQueue: this.pQ,
            cursor: this.cursor,
        });

        this.selLogic = then([first2Points, lastVertex], {
            onComplete: this.performConstruction.bind(this),
        });
    }

    handlePointerEvent(event: GeoPointerEvent) {
        const selected = this.selLogic.trySelect(event, docCtrl);

        // Hiển thị preview khác nhau theo chế độ
        if (this.mode === 0) {
            this.handleFocusPointsPreview(selected, docCtrl);
        } else {
            this.handleCenterVectorsPreview(selected, docCtrl);
        }
    }
}
```

### Pattern 8: Regular Polygon với Mouse Wheel

**Use case**: Tool tạo đa giác đều với khả năng thay đổi số cạnh bằng mouse wheel.

```typescript
export class CreateRegularPolygonTool extends GeometryTool {
    selLogic: ThenSelector;

    constructor() {
        super();
        // Đăng ký mouse wheel event
        this.registerMouseHandling({
            event: 'mousewheel',
            keys: ['ctrl'],
        });
        this.createSelLogic();
    }

    createSelLogic() {
        const first2Points = nPoints(this.pQ, this.cursor, {
            count: 2,
            onComplete: this.first2Points.bind(this),
        });

        // Điểm thứ 3 để xác định hướng và kích thước
        const thirdPoint = vertex({
            previewQueue: this.pQ,
            cursor: this.cursor,
            tfunc: this.transformThirdPoint.bind(this),
        });

        this.selLogic = then([first2Points, thirdPoint], {
            onComplete: this.performConstruction.bind(this),
        });
    }

    handleMouseWheel(event: WheelEvent) {
        if (event.ctrlKey) {
            // Thay đổi số cạnh đa giác
            this.toolState.edgeCount += event.deltaY > 0 ? 1 : -1;
            this.toolState.edgeCount = Math.max(3, Math.min(10, this.toolState.edgeCount));
            this.updatePreview();
        }
    }
}
```

### Pattern 9: Intersection Tool với Multiple Results

**Use case**: Tool tìm giao điểm với khả năng chọn từ nhiều giao điểm.

```typescript
export class IntersectionPointTool extends GeometryTool {
    selLogic: RepeatSelector<StrokeType>;
    intersectionPreview: RenderVertex[] = [];

    createSelLogic() {
        this.selLogic = repeat(
            stroke({
                selectableStrokeTypes: [
                    'RenderLine',
                    'RenderLineSegment',
                    'RenderVector',
                    'RenderRay',
                    'RenderCircle',
                    'RenderEllipse',
                    'RenderSector',
                ],
                previewQueue: this.pQ,
                cursor: this.cursor,
                highlightOnMatch: true,
            }),
            {
                count: 2,
                onComplete: this.performIntersectionPreview.bind(this),
            }
        );
    }

    doTrySelection(event: UIPointerEventData, ctrl: GeoDocCtrl) {
        // Kiểm tra click vào intersection preview
        if (this.intersectionPreview.length > 0) {
            const hitCtx = ctrl.editor.checkHitInternal(ctrl.layers[0], event, false, true);
            const hitEl = hitCtx?.hitDetails?.el;

            if (hitEl && hitEl.type === 'RenderVertex') {
                const previewPoint = this.intersectionPreview.find(p => p.relIndex === hitEl.relIndex);
                if (previewPoint && event.eventType === 'pointerup') {
                    this.performConstruction(ctrl, previewPoint);
                    return;
                }
            }
        }

        // Thử selection bình thường
        this.selLogic.trySelect(event, ctrl);
    }

    performIntersectionPreview(selector: RepeatSelector<StrokeType>, docCtrl: GeoDocCtrl) {
        const [stroke1, stroke2] = selector.selected;

        // Tính toán tất cả giao điểm
        const intersections = this.calculateIntersections(stroke1, stroke2);

        // Tạo preview cho tất cả giao điểm
        this.intersectionPreview = intersections.map((point, index) => pVertex(-1000 - index, point.coords));

        // Hiển thị preview
        this.intersectionPreview.forEach(preview => this.pQ.add(preview));

        // Enable filtering để chỉ có thể click vào intersection points
        if (this.intersectionPreview.length > 1) {
            this.editor.filterElementFunc = (el: GeoRenderElement) =>
                this.intersectionPreview.some(p => p.relIndex === el.relIndex);
        }
    }
}
```

### Pattern 10: Conditional Selection Logic

**Use case**: Logic selection thay đổi dựa trên context.

```typescript
export class SmartLineTool extends GeometryTool {
    createSelLogic() {
        this.selLogic = or(
            [
                // Nếu chọn 2 điểm -> tạo đường thẳng
                repeat(vertexS(this.pQ, this.cursor), {
                    count: 2,
                    name: 'twoPoints',
                }),

                // Nếu chọn 1 điểm + 1 đường -> tạo đường song song/vuông góc
                then([vertexS(this.pQ, this.cursor), strokeS(this.pQ, this.cursor)], { name: 'pointAndLine' }),

                // Nếu chọn 2 đường -> tạo đường qua giao điểm
                repeat(strokeS(this.pQ, this.cursor), {
                    count: 2,
                    name: 'twoLines',
                }),
            ],
            {
                onComplete: this.createSmartLine.bind(this),
            }
        );
    }

    createSmartLine(selector: OrSelector, doc: GeoDocCtrl) {
        const selectorName = this.getActiveSelectorName(selector);

        switch (selectorName) {
            case 'twoPoints':
                this.createLineFromPoints(selector.selected);
                break;
            case 'pointAndLine':
                this.createParallelOrPerpendicular(selector.selected);
                break;
            case 'twoLines':
                this.createLineThroughIntersection(selector.selected);
                break;
        }
    }
}
```

### Pattern 8: Dynamic Exclusion

**Use case**: Loại trừ elements dựa trên selection hiện tại.

```typescript
export class TriangleTool extends GeometryTool {
    selectedVertices: RenderVertex[] = [];

    createSelLogic() {
        this.selLogic = repeat(vertexS(this.pQ, this.cursor), {
            count: 3,
            onPartialSelection: this.onVertexSelected.bind(this),
        });

        // Cập nhật exclusion filter
        this.updateExclusionFilter();
    }

    onVertexSelected(
        newSel: SelectedVertex,
        curSel: SelectedVertex[],
        selector: RepeatSelector<SelectedVertex>,
        doc: GeoDocCtrl
    ): boolean {
        this.selectedVertices.push(vert(newSel));
        this.updateExclusionFilter();
        return true;
    }

    updateExclusionFilter() {
        const vertexSelector = this.selLogic.get('vertex') as VertexSelector;
        vertexSelector.setOption('refinedFilter', (el: RenderVertex) => {
            // Loại trừ các điểm đã chọn
            return !this.selectedVertices.some(v => v.relIndex === el.relIndex);
        });
    }
}
```

### Pattern 11: Semicircle Tool với Center Point

**Use case**: Tool tạo bán nguyệt với tự động tính center point.

```typescript
export class CreateSemicircleTool extends GeometryTool {
    selLogic: RepeatSelector<SelectedVertex>;
    previewSector: RenderSector;

    constructor() {
        super();
        this.selLogic = nPoints(this.pQ, this.cursor, {
            count: 2,
            onComplete: this.performConstruction.bind(this),
        });
    }

    doTrySelection(event: UIPointerEventData, ctrl: GeoDocCtrl) {
        const selected = this.selLogic.trySelect(event, ctrl);

        if (selected && selected.length >= 2) {
            const v1 = vert(selected[0]);
            const v2 = vert(selected[1]);

            // Tính center point tự động
            const centerCoords = [(v1.coords[0] + v2.coords[0]) / 2, (v1.coords[1] + v2.coords[1]) / 2];
            const centerPoint = pVertex(-22, centerCoords);

            // Tạo preview elements
            this.pQ.add(pLine(ctrl, -24, RenderLineSegment, v1, centerPoint));
            this.pQ.add(pLine(ctrl, -23, RenderLineSegment, centerPoint, v2));

            // Lưu sector để dùng trong construction
            this.previewSector = pSector(ctrl, -20, v1, centerPoint, v2);
            this.pQ.add(pSectorShape(ctrl, -21, v1, centerPoint, v2, this.previewSector));
        }

        this.pQ.flush(ctrl);
    }
}
```

### Pattern 12: Move Element Tool với Hit Detection

**Use case**: Tool di chuyển element với threshold detection và movement constraints.

```typescript
export class MoveElementTool extends GeometryTool {
    private pickedEl?: RenderVertex;
    private initialHit?: InitialHitInfo;
    private thresholdPassed: boolean = false;

    handlePointerEvent(event: GeoPointerEvent) {
        if (event.eventType === 'pointerdown') {
            this.handlePointerDown(event);
        } else if (event.eventType === 'pointermove') {
            this.handlePointerMove(event);
        } else if (event.eventType === 'pointerup') {
            this.handlePointerUp(event);
        }
    }

    private handlePointerDown(event: GeoPointerEvent) {
        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) return;

        // Hit test để tìm element có thể di chuyển
        const hitCtx = ctrl.editor.checkHitInternal(ctrl.layers[0], event);
        const hitEl = hitCtx?.hitDetails?.el;

        if (hitEl && hitEl.type === 'RenderVertex') {
            this.initialHit = {
                el: hitEl as RenderVertex,
                ctrl: ctrl,
                screenPos: event.screenPosition,
            };
        }
    }

    private handlePointerMove(event: GeoPointerEvent) {
        if (!this.initialHit) return;

        // Kiểm tra threshold để bắt đầu move
        const distance = this.calculateDistance(this.initialHit.screenPos, event.screenPosition);

        if (!this.thresholdPassed && distance > MOVE_THRESHOLD) {
            this.thresholdPassed = true;
            this.startMoving(this.initialHit.el);
        }

        if (this.thresholdPassed && this.pickedEl) {
            this.updateElementPosition(event);
        }
    }

    private startMoving(element: RenderVertex) {
        this.pickedEl = element;
        // Tạo preview element
        this.createMovementPreview(element);
        // Xác định movement constraints (line, circle, etc.)
        this.determineMovementConstraints(element);
    }
}
```

### Pattern 13: Point on Object Tool

**Use case**: Tool tạo điểm trên object với VertexOnStrokeSelector đơn giản.

```typescript
export class PointOnObjectTool extends GeometryTool {
    selLogic: VertexOnStrokeSelector;

    constructor() {
        super();
        this.selLogic = vertexOnStroke({
            previewQueue: this.pQ,
            syncPreview: true,
            cursor: this.cursor,
            onComplete: this.performConstruction.bind(this),
        });
    }

    // Tool này chỉ cần một selector đơn giản
    // Không cần logic phức tạp vì VertexOnStrokeSelector đã xử lý tất cả
    handlePointerEvent(event: GeoPointerEvent) {
        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        this.selLogic.trySelect(event, ctrl);
        this.pQ.flush(ctrl);
    }

    async performConstruction(selector: VertexOnStrokeSelector, ctrl: GeoDocCtrl) {
        // selector.selected có dạng [StrokeType, RenderVertex]
        const [stroke, pointOnStroke] = selector.selected;

        // Tạo construction request cho point on object
        const construction = this.buildPointOnElConstruction(pointOnStroke, stroke);
        await this.executeConstruction(construction, ctrl);
    }
}
```

### Pattern 14: Tools không sử dụng Selector

**Use case**: Một số tools không sử dụng Selector System mà xử lý pointer events trực tiếp.

```typescript
export class CreateTrapezoidTool extends GeometryTool {
    private points: RenderVertex[] = [];
    private clickCount = 0;

    // Sử dụng PotentialSelectionDelegator thay vì Selector
    private potentialSelectionDelegator = new PotentialSelectionDelegator(this);

    // Filter function để loại trừ điểm đã chọn
    protected filterElementFunc = (el: GeoRenderElement) => {
        return el.type == 'RenderVertex' && this.points.filter(p => p.relIndex == el.relIndex).length < 1;
    };

    handlePointerEvent(event: GeoPointerEvent) {
        switch (event.nativeEvent.type) {
            case 'pointerdown':
                this.onPointerDown(event);
                break;
            case 'pointerup':
                this.onPointerUp(event);
                break;
            case 'pointermove':
                this.onPointerMove(event);
                break;
        }
    }

    private async onPointerDown(event: GeoPointerEvent) {
        // Xử lý hit detection thủ công
        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        const hitCtx = ctrl.editor.checkHitInternal(ctrl.layers[0], event);

        if (hitCtx?.hitDetails?.el) {
            // Thêm điểm đã chọn
            this.points.push(hitCtx.hitDetails.el as RenderVertex);
            this.clickCount++;

            if (this.clickCount === 4) {
                await this.createTrapezoid();
            }
        }
    }
}
```

### Pattern 15: Isosceles Right Triangle với Preview Points

**Use case**: Tool tạo tam giác vuông cân với multiple preview points.

```typescript
export class CreateIsoscelesRightTriangleTool extends GeometryTool {
    private points: RenderVertex[] = [];
    private previewPoints: RenderVertex[] = [];
    private selectedPreviewPoint: RenderVertex | null = null;

    // Tương tự CreateTrapezoidTool, không dùng Selector
    protected filterElementFunc = (el: GeoRenderElement) => {
        return el.type === 'RenderVertex' && this.points.filter(p => p.relIndex === el.relIndex).length < 1;
    };

    private async handleFirstPoint(event: GeoPointerEvent) {
        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        const { point } = await getPointAndVertex(event, ctrl, this.editor);

        this.points.push(point);
        // Sau khi chọn điểm đầu, tool chuyển sang chế độ chọn điểm thứ 2
    }

    private async handleSecondPoint(event: GeoPointerEvent) {
        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        const { point } = await getPointAndVertex(event, ctrl, this.editor);

        this.points.push(point);
        // Tạo 2 preview points cho điểm thứ 3 (2 vị trí có thể tạo tam giác vuông cân)
        this.generatePreviewPoints(ctrl);
    }

    private generatePreviewPoints(ctrl: GeoDocCtrl) {
        const [p1, p2] = this.points;

        // Tính 2 vị trí có thể cho điểm thứ 3
        const positions = this.calculateIsoscelesRightPositions(p1, p2);

        this.previewPoints = positions.map((pos, index) => pVertex(-100 - index, pos));

        // Hiển thị preview points
        this.previewPoints.forEach(p => this.pQ.add(p));
        this.pQ.flush(ctrl);
    }

    private async selectThirdPointPosition(event: GeoPointerEvent, isPreview: boolean) {
        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);

        // Kiểm tra click vào preview point nào
        const hitCtx = ctrl.editor.checkHitInternal(ctrl.layers[0], event, false, true);
        const hitEl = hitCtx?.hitDetails?.el;

        if (hitEl && this.previewPoints.some(p => p.relIndex === hitEl.relIndex)) {
            this.selectedPreviewPoint = hitEl as RenderVertex;

            if (!isPreview) {
                // Hoàn thành selection và tạo triangle
                await this.createIsoscelesRightTriangle();
            }
        }
    }
}
```

### Pattern 16: Non-Selector Tools

**Use case**: Tools không cần selection như RenameElementTool, ListElementTool.

```typescript
export class RenameElementTool extends GeometryTool {
    // Tool này không sử dụng selector, chỉ lắng nghe selection events

    constructor() {
        super();
        // Lắng nghe viewport content events thay vì pointer events
        this.viewportContentEventListener = new ViewportContentEventListener(this);
    }

    // Xử lý thay đổi từ UI thay vì pointer events
    protected async processChangeToolEvent(event: GeoToolEventData) {
        const changeEvent = event as ChangeToolEventData;

        if (changeEvent.changes.has('name')) {
            const newName = changeEvent.changes.get('name').currentValue;
            await this.renameElement(newName);
        }

        return event;
    }

    private async renameElement(newName: string) {
        // Thực hiện rename element
        const cmd = new UpdateElementNameCmd();
        cmd.setName(newName);
        cmd.setRelIndex(this.toolState.relIndex);

        await this.editor.cmdChannel.receive(cmd);
    }
}

export class ListElementTool extends GeometryTool {
    // Tool này cũng không dùng selector, chỉ xử lý actions từ UI

    protected async processChangeToolEvent(event: GeoToolEventData) {
        const changeEvent = event as ChangeToolEventData;

        if (changeEvent.changes?.has('action')) {
            const action = changeEvent.changes.get('action').currentValue;

            switch (action.actionType) {
                case 'hide':
                    this.setHidingElement(doc, action.relIdx, true);
                    break;
                case 'show':
                    this.setHidingElement(doc, action.relIdx, false);
                    break;
                case 'highlight':
                    this.editor.highlight(hitContext);
                    break;
                case 'select':
                    this.editor.selectElement(hitContext, true);
                    break;
            }
        }

        return event;
    }
}
```

### Pattern 17: Angle Tool với Refined Filter

**Use case**: Tool tạo góc từ 2 đường thẳng + điểm chỉ hướng, với filter loại trừ endpoints của đường đã chọn.

```typescript
export class CreateAngleTool extends GeometryTool {
    selLogic: ThenSelector;

    createSelectionLogic() {
        // Stage 1: Chọn 2 đường thẳng
        const lineSelector = nLines(this.pQ, this.cursor, {
            count: 2,
            name: 'lineSelector',
        });

        // Stage 2: Chọn điểm chỉ hướng với refined filter
        const directionPointSelector = vertex({
            previewQueue: this.pQ,
            cursor: this.cursor,
            refinedFilter: (vertex: RenderVertex) => {
                // Loại trừ các endpoints của đường đã chọn
                const selectedLines = this.selLogic.selected[0] as RenderLine[];
                return !selectedLines.some((selectedStroke: any) => {
                    const line = Array.isArray(selectedStroke) ? selectedStroke[0] : selectedStroke;
                    return line.startPointIdx === vertex.relIndex || line.endPointIdx === vertex.relIndex;
                });
            },
        });

        this.selLogic = then([lineSelector, directionPointSelector], {
            onComplete: this.performConstructionFromSelection.bind(this),
        });
    }
}
```

### Pattern 18: Bisector Tool với Transform Function

**Use case**: Tool tạo đường phân giác với transform function chiếu điểm lên tia phân giác.

```typescript
export class CreateBisectorLineTool extends GeometryTool {
    selLogic: ThenSelector;
    selectedAngle: RenderAngle | undefined;
    bisectorVector: number[] | undefined;
    rayPreview: RenderRay | undefined;

    createSelLogic() {
        // Chọn góc trước
        const angleSelector = stroke({
            selectableStrokeTypes: ['RenderAngle'],
            previewQueue: this.pQ,
            cursor: this.cursor,
        });

        // Chọn điểm cuối với transform function
        const vertexSelector = vertex({
            previewQueue: this.pQ,
            cursor: this.cursor,
            tfunc: (previewEl: RenderVertex, doc: GeoDocCtrl) => this.projectOnBisectorRay(previewEl, doc),
        });

        this.selLogic = then([angleSelector, vertexSelector], {
            onComplete: (selector: ThenSelector, doc: GeoDocCtrl) => {
                const [angle, endSelection] = selector.selected;
                this.performConstruction(doc, angle as RenderAngle, endSelection);
            },
        });
    }

    // Transform function phức tạp
    projectOnBisectorRay(previewEl: RenderVertex, doc: GeoDocCtrl): RenderVertex {
        if (!this.selectedAngle || !this.bisectorVector || !this.rayPreview) {
            return previewEl;
        }

        try {
            const angleCoords = this.selectedAngle.coord('root', doc.rendererCtrl);
            const projectedCoords = projectPointOntoLine(previewEl.coords, angleCoords, this.bisectorVector);

            if (projectedCoords) {
                // Kiểm tra hướng của tia
                const pointVector = [projectedCoords[0] - angleCoords[0], projectedCoords[1] - angleCoords[1]];
                const dotProduct = pointVector[0] * this.bisectorVector[0] + pointVector[1] * this.bisectorVector[1];

                if (dotProduct >= 0) {
                    // Projection hợp lệ trên tia
                    previewEl.coords[0] = projectedCoords[0];
                    previewEl.coords[1] = projectedCoords[1];
                } else {
                    // Projection phía sau, dùng điểm bắt đầu tia
                    previewEl.coords[0] = angleCoords[0];
                    previewEl.coords[1] = angleCoords[1];
                }
            }
        } catch (error) {
            console.warn('Projection failed:', error);
        }

        return previewEl;
    }
}
```

### Pattern 19: Circular Sector với Circle Transform

**Use case**: Tool tạo cung tròn với điểm cuối bị ràng buộc trên đường tròn.

```typescript
export class CreateSectorTool extends GeometryTool {
    selLogic: ThenSelector;
    previewSector: any = null;

    createSelLogic() {
        // Chọn 2 điểm đầu (tâm và điểm xác định bán kính)
        const first2Points = nPoints(this.pQ, this.cursor, {
            count: 2,
            onPartialSelection: (newSel: SelectedVertex, curSel, selector, doc) => {
                return true;
            },
        });

        // Điểm cuối với circle transform hoặc vertex on stroke
        const endPointSelector = or(
            [
                vertex({
                    previewQueue: this.pQ,
                    cursor: this.cursor,
                    // Transform để ràng buộc trên đường tròn
                    tfunc: el =>
                        circleTransform(
                            this.selLogic.selected[0] as SelectedVertex[],
                            el,
                            0 // Tâm là điểm đầu tiên
                        ),
                }),
                vertexOnStroke({
                    selectableStrokeTypes: [
                        'RenderCircle',
                        'RenderLine',
                        'RenderLineSegment',
                        'RenderVector',
                        'RenderRay',
                        'RenderSector',
                    ],
                }),
            ],
            {
                flatten: true,
            }
        );

        this.selLogic = then([first2Points, endPointSelector], {
            onComplete: (selector: ThenSelector, doc) => this.performConstruction(selector, doc),
        });
    }

    // Keyboard event handling cho clockwise/counterclockwise
    handleKeyboardEvent(event: GeoKeyboardEvent): GeoKeyboardEvent {
        if (event.eventType == 'keyup' && event.getKeys.includes('shift')) {
            this.toolState.clockwise = !this.toolState.clockwise;
            this.toolbar.update(this.toolType, this.toolState);
        }
        return event;
    }
}
```

### Pattern 20: Middle Point Tool với Multiple Input Types

**Use case**: Tool tạo điểm giữa với 2 input types khác nhau - 2 điểm hoặc 1 line segment.

```typescript
export class MiddlePointTool extends GeometryTool {
    selLogic: OrSelector<SelectedInput>;
    points: RenderVertex[] = [];

    createSelLogic() {
        this.selLogic = or<[RenderVertex, RenderVertex] | [RenderLineSegment]>(
            [
                // Option 1: Chọn 2 điểm
                repeat<RenderVertex>(
                    vertex({
                        genPreview: false,
                        previewQueue: this.pQ,
                        cursor: this.cursor,
                        refinedFilter: this.excludeSelection.bind(this),
                        onComplete: selector => this.points.push(selector.selected),
                    }),
                    { count: 2 }
                ),

                // Option 2: Chọn line segment với transform
                vertexOnStroke({
                    selectableStrokeTypes: ['RenderLineSegment', 'RenderVector', 'RenderSector'],
                    cursor: this.cursor,
                    tfunc: this.pointOnObjectTransform.bind(this),
                    syncPreview: true,
                    genPreview: true,
                }),
            ],
            {
                flatten: true,
                onComplete: this.performConstruction.bind(this),
            }
        );
    }

    excludeSelection(el: RenderVertex) {
        // Loại trừ điểm đã chọn
        return !this.points.includes(el);
    }

    pointOnObjectTransform(stroke: any, vertex: RenderVertex, doc: GeoDocCtrl): RenderVertex {
        // Transform để tạo middle point trên object
        return this.calculateMiddlePointOnStroke(stroke, vertex);
    }
}
```

### Pattern 21: Parallel Line Tool với Utility Function

**Use case**: Tool tạo đường song song sử dụng utility function để tái sử dụng logic.

```typescript
export class CreateParallelLineTool extends GeometryTool {
    selLogic: ThenSelector;
    selectedLine: RenderLine | undefined;
    selectedPoint: RenderVertex | undefined;
    previewLine: RenderLine | undefined;

    createSelLogic() {
        // Sử dụng utility function từ parallel_perpendicular.line.tool.utils
        this.selLogic = createLineToolSelLogic(
            this.pQ,
            this.pointerHandler,
            async (selector: ThenSelector, doc: GeoDocCtrl) => {
                const selected = selector.selected;
                if (!selected || selected.length < 3) return;

                this.selectedLine = selected[0] as RenderLine;
                this.selectedPoint = selected[1] as RenderVertex;
                const finalVertex = vert(selected[2] as RenderVertex | VertexOnStroke);
                const finalVertexSelection = selected[2];

                await this.performConstructionWithFinalVertex(
                    doc,
                    finalVertex,
                    finalVertexSelection as RenderVertex | VertexOnStroke
                );
            }
        );
    }

    // Logic phức tạp để xác định loại construction
    async performConstructionWithFinalVertex(
        ctrl: GeoDocCtrl,
        finalVertex: RenderVertex,
        finalVertexSelection?: RenderVertex | VertexOnStroke
    ) {
        if (!this.selectedLine || !this.selectedPoint) return;

        // Logic:
        // - Nếu vertex cuối KHÔNG trên preview line -> tạo infinite line
        // - Nếu vertex cuối TRÊN preview line -> tạo line segment
        const isOnPreviewLine = this.checkIfVertexOnPreviewLine(finalVertex);

        if (isOnPreviewLine) {
            await this.buildLineSegment(ctrl, finalVertex);
        } else {
            await this.buildInfiniteLine(ctrl, finalVertex);
        }
    }
}
```

## Best Practices cho Patterns

### 1. Tách biệt Logic và UI

```typescript
// ✅ Tốt - Logic selection tách biệt
export class GeometryTool {
    protected createSelLogic() {
        // Chỉ định nghĩa logic selection
    }

    protected handleSelection(selected: any) {
        // Xử lý kết quả selection
    }

    protected updatePreview(selected: any) {
        // Cập nhật preview UI
    }
}
```

### 2. Sử dụng Named Selectors

```typescript
// ✅ Tốt - Đặt tên cho selectors
const pointSelector = vertex({ name: 'centerPoint' });
const lineSelector = stroke({ name: 'baseLine' });

// Dễ dàng truy cập sau này
const centerPoint = this.selLogic.get('centerPoint');
```

### 3. Quản lý State đúng cách

```typescript
// ✅ Tốt - Reset state khi cần
override resetState() {
    this.selectedVertices = [];
    this.previewElements = [];
    this.selLogic.reset();
    super.resetState();
}
```

### 4. Error Handling

```typescript
// ✅ Tốt - Xử lý lỗi gracefully
handleSelection(selector: any, doc: GeoDocCtrl) {
    try {
        if (!selector.selected || selector.selected.length === 0) {
            this.showMessage('Không có phần tử nào được chọn');
            return;
        }

        this.processSelection(selector.selected);
    } catch (error) {
        this.showError('Lỗi xử lý selection: ' + error.message);
        this.resetState();
    }
}
```

Các patterns này cung cấp foundation vững chắc để xây dựng các geometry tools phức tạp với logic selection linh hoạt và dễ bảo trì.
