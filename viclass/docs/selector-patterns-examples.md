# Selector Patterns và Use Cases

## Tổng quan

Document này mô tả các patterns phổ biến và use cases thực tế khi sử dụng Selector APIs trong VIClass Geometry Editor.

## Patterns cơ bản

### Pattern 1: Simple Point Selection

**Use case**: Tạo điểm đơn giản, chọn điểm có sẵn hoặc tạo mới.

```typescript
export class CreatePointTool extends GeometryTool {
    selLogic: VertexSelector;
    
    constructor() {
        this.selLogic = vertex({
            preview: true,      // Cho phép tạo điểm mới
            renderEl: true,     // Cho phép chọn điểm có sẵn
            onComplete: this.createPoint.bind(this)
        });
    }
}
```

### Pattern 2: Two Points Line Creation

**Use case**: Tạo đường thẳng từ 2 điểm.

```typescript
export class CreateLineTool extends GeometryTool {
    selLogic: RepeatSelector<SelectedVertex>;
    
    constructor() {
        this.selLogic = repeat(vertexS(this.pQ, this.cursor), {
            count: 2,
            onComplete: this.createLine.bind(this)
        });
    }
    
    handlePointerEvent(event: GeoPointerEvent) {
        const selected = this.selLogic.trySelect(event, docCtrl);
        
        // Hiển thị preview line khi có 2 điểm
        if (selected && selected.length === 2) {
            this.showPreviewLine(vert(selected[0]), vert(selected[1]));
        }
    }
}
```

### Pattern 3: Point on Object

**Use case**: Tạo điểm trên đối tượng khác (đường, đường tròn).

```typescript
export class PointOnObjectTool extends GeometryTool {
    selLogic: OrSelector<SelectedVertex>;
    
    constructor() {
        this.selLogic = or([
            vertex({ preview: true }),  // Điểm tự do
            vertexOnStroke({           // Điểm trên stroke
                selectableStrokeTypes: ['RenderLine', 'RenderCircle'],
                syncPreview: true
            })
        ], {
            flatten: true,
            onComplete: this.createPoint.bind(this)
        });
    }
}
```

## Patterns phức tạp

### Pattern 4: Sequential Selection

**Use case**: Chọn các phần tử theo thứ tự cụ thể.

```typescript
export class PerpendicularLineTool extends GeometryTool {
    selLogic: ThenSelector;
    
    createSelLogic() {
        const lineSelector = stroke({
            selectableStrokeTypes: ['RenderLine'],
            name: 'baseLine'
        });
        
        const pointSelector = vertexS(this.pQ, this.cursor);
        pointSelector.get('vertex').setOption('name', 'throughPoint');
        
        this.selLogic = then([lineSelector, pointSelector], {
            onComplete: this.createPerpendicularLine.bind(this)
        });
    }
    
    createPerpendicularLine(selector: ThenSelector, doc: GeoDocCtrl) {
        const [baseLine, throughPoint] = selector.selected;
        // Tạo đường vuông góc
        this.constructPerpendicularLine(strk(baseLine), vert(throughPoint));
    }
}
```

### Pattern 5: Constrained Selection

**Use case**: Chọn với ràng buộc hình học.

```typescript
export class RightTriangleTool extends GeometryTool {
    selLogic: ThenSelector;
    
    createSelLogic() {
        // Chọn 2 điểm đầu tự do
        const first2Points = nPoints(this.pQ, this.cursor, { count: 2 });
        
        // Điểm thứ 3 bị ràng buộc tạo góc vuông
        const thirdPoint = vertex({
            preview: true,
            tfunc: (previewEl, doc) => {
                const selectedPoints = this.selLogic.selected[0] as SelectedVertex[];
                return perpLinesTransform(selectedPoints, previewEl);
            },
            cfunc: (el, doc) => {
                const selectedPoints = this.selLogic.selected[0] as SelectedVertex[];
                return perpLinesCheck(selectedPoints, el);
            }
        });
        
        this.selLogic = then([first2Points, thirdPoint], {
            onComplete: this.createRightTriangle.bind(this)
        });
    }
}
```

### Pattern 6: Multi-mode Selection

**Use case**: Tool có nhiều chế độ selection khác nhau.

```typescript
export class AngleTool extends GeometryTool {
    selLogic: OrSelector;
    
    createSelLogic() {
        // Chế độ 1: Chọn góc có sẵn
        const existingAngleSelector = stroke({
            selectableStrokeTypes: ['RenderAngle'],
            name: 'existingAngle'
        });
        
        // Chế độ 2: Chọn 2 đường thẳng + điểm chỉ hướng
        const lineBasedSelector = then([
            repeat(stroke({
                selectableStrokeTypes: ['RenderLine'],
                name: 'line'
            }), { count: 2 }),
            vertex({ name: 'directionPoint' })
        ]);
        
        // Chế độ 3: Chọn 3 điểm
        const pointBasedSelector = nPoints(this.pQ, this.cursor, { 
            count: 3,
            name: 'threePoints'
        });
        
        this.selLogic = or([
            existingAngleSelector,
            lineBasedSelector,
            pointBasedSelector
        ], {
            onComplete: this.createAngle.bind(this)
        });
    }
    
    createAngle(selector: OrSelector, doc: GeoDocCtrl) {
        const selected = selector.selected;
        
        // Xử lý theo loại selection
        if (this.isExistingAngle(selected)) {
            this.processExistingAngle(selected);
        } else if (this.isLineBased(selected)) {
            this.processLineBased(selected);
        } else {
            this.processPointBased(selected);
        }
    }
}
```

## Use Cases thực tế

### Use Case 1: Polygon Tool

**Mô tả**: Tạo đa giác với số điểm không giới hạn, kết thúc bằng double-click hoặc click vào điểm đầu.

```typescript
export class PolygonTool extends GeometryTool {
    selLogic: RepeatSelector<SelectedVertex>;
    selectedPoints: RenderVertex[] = [];
    
    constructor() {
        this.selLogic = repeat(vertexS(this.pQ, this.cursor), {
            onPartialSelection: this.onPointSelected.bind(this),
            // Không set count để cho phép chọn không giới hạn
        });
    }
    
    onPointSelected(newSel: SelectedVertex, curSel: SelectedVertex[], selector: RepeatSelector<SelectedVertex>, doc: GeoDocCtrl): boolean {
        const newPoint = vert(newSel);
        this.selectedPoints.push(newPoint);
        
        // Kiểm tra kết thúc polygon
        if (this.selectedPoints.length >= 3) {
            // Nếu click vào điểm đầu tiên -> kết thúc
            if (this.isClickOnFirstPoint(newPoint)) {
                this.completePolygon();
                return false; // Dừng repeat
            }
        }
        
        // Hiển thị preview edges
        this.updatePreviewPolygon();
        return true; // Tiếp tục chọn
    }
    
    handleDoubleClick(event: GeoPointerEvent) {
        if (this.selectedPoints.length >= 3) {
            this.completePolygon();
        }
    }
}
```

### Use Case 2: Circle Tool với nhiều chế độ

**Mô tả**: Tạo đường tròn bằng center-radius, 2 điểm, hoặc 3 điểm.

```typescript
export class CircleTool extends GeometryTool {
    mode: 'center-radius' | 'two-points' | 'three-points' = 'center-radius';
    selLogic: OrSelector;
    
    createSelLogic() {
        // Chế độ center-radius: chọn tâm rồi chọn điểm trên đường tròn
        const centerRadiusSelector = then([
            vertexS(this.pQ, this.cursor), // Tâm
            vertexS(this.pQ, this.cursor)  // Điểm trên đường tròn
        ]);
        
        // Chế độ 2 điểm: đường tròn có đường kính là đoạn thẳng nối 2 điểm
        const twoPointsSelector = nPoints(this.pQ, this.cursor, { count: 2 });
        
        // Chế độ 3 điểm: đường tròn đi qua 3 điểm
        const threePointsSelector = nPoints(this.pQ, this.cursor, { count: 3 });
        
        this.selLogic = or([
            centerRadiusSelector,
            twoPointsSelector,
            threePointsSelector
        ], {
            onComplete: this.createCircle.bind(this)
        });
    }
    
    createCircle(selector: OrSelector, doc: GeoDocCtrl) {
        const selected = selector.selected;
        
        if (selected.length === 2 && this.mode === 'center-radius') {
            this.createCenterRadiusCircle(selected);
        } else if (selected.length === 2 && this.mode === 'two-points') {
            this.createDiameterCircle(selected);
        } else if (selected.length === 3) {
            this.createThreePointCircle(selected);
        }
    }
}
```

### Use Case 3: Intersection Tool

**Mô tả**: Tìm giao điểm của 2 đối tượng.

```typescript
export class IntersectionTool extends GeometryTool {
    selLogic: RepeatSelector<SelectedStroke>;
    
    constructor() {
        this.selLogic = repeat(strokeS(this.pQ, this.cursor), {
            count: 2,
            onComplete: this.findIntersections.bind(this)
        });
    }
    
    findIntersections(selector: RepeatSelector<SelectedStroke>, doc: GeoDocCtrl) {
        const [stroke1, stroke2] = selector.selected.map(s => strk(s));
        
        // Tính toán giao điểm
        const intersections = this.calculateIntersections(stroke1, stroke2);
        
        if (intersections.length === 0) {
            this.showMessage('Không có giao điểm');
            return;
        }
        
        // Tạo điểm giao
        intersections.forEach(point => {
            this.createIntersectionPoint(point, stroke1, stroke2);
        });
    }
}
```

### Use Case 4: Symmetry Tool

**Mô tả**: Tạo đối xứng qua điểm hoặc đường thẳng.

```typescript
export class SymmetryTool extends GeometryTool {
    mode: 'point' | 'line' = 'point';
    selLogic: ThenSelector;
    
    createSelLogic() {
        // Chọn đối tượng cần đối xứng
        const objectSelector = or([
            vertexS(this.pQ, this.cursor),
            strokeS(this.pQ, this.cursor)
        ], { flatten: true });
        
        // Chọn trục đối xứng
        const axisSelector = this.mode === 'point' 
            ? vertexS(this.pQ, this.cursor)  // Điểm đối xứng
            : strokeS(this.pQ, this.cursor); // Đường đối xứng
        
        this.selLogic = then([objectSelector, axisSelector], {
            onComplete: this.createSymmetry.bind(this)
        });
    }
    
    createSymmetry(selector: ThenSelector, doc: GeoDocCtrl) {
        const [object, axis] = selector.selected;
        
        if (this.mode === 'point') {
            this.createPointSymmetry(object, vert(axis));
        } else {
            this.createLineSymmetry(object, strk(axis));
        }
    }
}
```

## Advanced Patterns

### Pattern 7: Conditional Selection Logic

**Use case**: Logic selection thay đổi dựa trên context.

```typescript
export class SmartLineTool extends GeometryTool {
    createSelLogic() {
        this.selLogic = or([
            // Nếu chọn 2 điểm -> tạo đường thẳng
            repeat(vertexS(this.pQ, this.cursor), { 
                count: 2,
                name: 'twoPoints'
            }),
            
            // Nếu chọn 1 điểm + 1 đường -> tạo đường song song/vuông góc
            then([
                vertexS(this.pQ, this.cursor),
                strokeS(this.pQ, this.cursor)
            ], { name: 'pointAndLine' }),
            
            // Nếu chọn 2 đường -> tạo đường qua giao điểm
            repeat(strokeS(this.pQ, this.cursor), {
                count: 2,
                name: 'twoLines'
            })
        ], {
            onComplete: this.createSmartLine.bind(this)
        });
    }
    
    createSmartLine(selector: OrSelector, doc: GeoDocCtrl) {
        const selectorName = this.getActiveSelectorName(selector);
        
        switch (selectorName) {
            case 'twoPoints':
                this.createLineFromPoints(selector.selected);
                break;
            case 'pointAndLine':
                this.createParallelOrPerpendicular(selector.selected);
                break;
            case 'twoLines':
                this.createLineThroughIntersection(selector.selected);
                break;
        }
    }
}
```

### Pattern 8: Dynamic Exclusion

**Use case**: Loại trừ elements dựa trên selection hiện tại.

```typescript
export class TriangleTool extends GeometryTool {
    selectedVertices: RenderVertex[] = [];
    
    createSelLogic() {
        this.selLogic = repeat(vertexS(this.pQ, this.cursor), {
            count: 3,
            onPartialSelection: this.onVertexSelected.bind(this)
        });
        
        // Cập nhật exclusion filter
        this.updateExclusionFilter();
    }
    
    onVertexSelected(newSel: SelectedVertex, curSel: SelectedVertex[], selector: RepeatSelector<SelectedVertex>, doc: GeoDocCtrl): boolean {
        this.selectedVertices.push(vert(newSel));
        this.updateExclusionFilter();
        return true;
    }
    
    updateExclusionFilter() {
        const vertexSelector = this.selLogic.get('vertex') as VertexSelector;
        vertexSelector.setOption('refinedFilter', (el: RenderVertex) => {
            // Loại trừ các điểm đã chọn
            return !this.selectedVertices.some(v => v.relIndex === el.relIndex);
        });
    }
}
```

## Best Practices cho Patterns

### 1. Tách biệt Logic và UI

```typescript
// ✅ Tốt - Logic selection tách biệt
export class GeometryTool {
    protected createSelLogic() {
        // Chỉ định nghĩa logic selection
    }
    
    protected handleSelection(selected: any) {
        // Xử lý kết quả selection
    }
    
    protected updatePreview(selected: any) {
        // Cập nhật preview UI
    }
}
```

### 2. Sử dụng Named Selectors

```typescript
// ✅ Tốt - Đặt tên cho selectors
const pointSelector = vertex({ name: 'centerPoint' });
const lineSelector = stroke({ name: 'baseLine' });

// Dễ dàng truy cập sau này
const centerPoint = this.selLogic.get('centerPoint');
```

### 3. Quản lý State đúng cách

```typescript
// ✅ Tốt - Reset state khi cần
override resetState() {
    this.selectedVertices = [];
    this.previewElements = [];
    this.selLogic.reset();
    super.resetState();
}
```

### 4. Error Handling

```typescript
// ✅ Tốt - Xử lý lỗi gracefully
handleSelection(selector: any, doc: GeoDocCtrl) {
    try {
        if (!selector.selected || selector.selected.length === 0) {
            this.showMessage('Không có phần tử nào được chọn');
            return;
        }
        
        this.processSelection(selector.selected);
    } catch (error) {
        this.showError('Lỗi xử lý selection: ' + error.message);
        this.resetState();
    }
}
```

Các patterns này cung cấp foundation vững chắc để xây dựng các geometry tools phức tạp với logic selection linh hoạt và dễ bảo trì.
