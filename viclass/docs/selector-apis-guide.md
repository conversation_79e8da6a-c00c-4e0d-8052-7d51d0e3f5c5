# Hướng dẫn sử dụng Selector APIs

## Tổng quan

Selector System là một hệ thống mạnh mẽ trong VIClass Geometry Editor cho phép các tools thực hiện logic chọn lựa phần tử một cách nhất quán và linh hoạt. Hệ thống này cung cấp:

- **Selectors cơ bản**: <PERSON><PERSON><PERSON> các loại phần tử khác nhau (điểm, đường, hình)
- **Selection DSL**: Ngôn ngữ đặc thù miền để tạo logic chọn lựa phức tạp
- **Common utilities**: <PERSON><PERSON><PERSON> hàm tiện ích cho các trường hợp sử dụng phổ biến

## Quy trình sử dụng Selector

1. **Khởi tạo selector** với các options phù hợp
2. **Gọi trySelect()** để kiểm tra phần tử có thể được chọn
3. **Xử lý highlight** và preview tự động
4. **Nhận callback onComplete** khi selection hoàn thành
5. **Reset selector** khi cần bắt đầu lại

## Selector cơ bản

### VertexSelector - Chọn điểm

Dùng để chọn các điểm (vertices) trong document.

#### Khởi tạo cơ bản:
```typescript
import { vertex } from '../selectors';

const pointSelector = vertex({
    preview: true,           // Cho phép tạo điểm preview
    renderEl: true,          // Cho phép chọn điểm có sẵn
    autoAcceptOnPointerUp: true,  // Tự động accept khi thả chuột
    highlightOnMatch: true,  // Highlight khi hover
});
```

#### Options quan trọng:

- **`preview`**: `boolean` - Cho phép chọn preview elements
- **`renderEl`**: `boolean` - Cho phép chọn render elements có sẵn
- **`genPreview`**: `boolean` - Tự động tạo preview point khi di chuyển chuột
- **`stickyOnMatch`**: `boolean` - Giữ element "dính" khi được chọn
- **`stickyPTypes`**: `string[]` - Loại pointer áp dụng sticky (touch, mouse, pen)
- **`stopSnap`**: `boolean` - Tắt snap to grid
- **`tfunc`**: `(previewEl, doc) => RenderVertex` - Transform function cho preview
- **`cfunc`**: `(el, doc) => boolean` - Check function để validate selection
- **`refinedFilter`**: `(el) => boolean` - Filter bổ sung cho elements

#### Ví dụ với transform function:
```typescript
const constrainedPointSelector = vertex({
    preview: true,
    tfunc: (previewEl, doc) => {
        // Ràng buộc điểm trên đường thẳng
        return projectPointOnLine(previewEl, targetLine);
    },
    cfunc: (el, doc) => {
        // Kiểm tra điểm có hợp lệ không
        return isValidPoint(el);
    }
});
```

### StrokeSelector - Chọn đường/hình

Dùng để chọn các phần tử dạng stroke (đường thẳng, đường cong, hình).

#### Khởi tạo:
```typescript
import { stroke } from '../selectors';

const lineSelector = stroke({
    selectableStrokeTypes: ['RenderLine', 'RenderLineSegment'],
    highlightOnMatch: true,
    autoAcceptOnPointerUp: true,
});
```

#### Các loại stroke có thể chọn:
- `'RenderLine'` - Đường thẳng vô hạn
- `'RenderLineSegment'` - Đoạn thẳng
- `'RenderVector'` - Vector
- `'RenderRay'` - Tia
- `'RenderCircle'` - Đường tròn
- `'RenderEllipse'` - Ellipse
- `'RenderSector'` - Cung tròn

### VertexOnStrokeSelector - Chọn điểm trên đường

Selector đặc biệt cho phép chọn một điểm trên stroke và tự động tạo preview point.

#### Khởi tạo:
```typescript
import { vertexOnStroke } from '../selectors';

const pointOnLineSelector = vertexOnStroke({
    selectableStrokeTypes: ['RenderLine', 'RenderCircle'],
    syncPreview: true,
    tfunc: (stroke, previewVertex, doc) => {
        // Transform điểm trên stroke
        return transformPointOnStroke(stroke, previewVertex);
    }
});
```

## Selection DSL - Ngôn ngữ đặc thù miền

### OrSelector - Chọn một trong nhiều

Cho phép chọn một trong nhiều loại phần tử khác nhau.

#### Cú pháp:
```typescript
import { or, vertex, stroke } from '../selectors';

const pointOrLineSelector = or([
    vertex({ preview: true }),
    stroke({ selectableStrokeTypes: ['RenderLine'] })
], {
    flatten: true,  // Flatten kết quả
    onComplete: (selector, doc) => {
        // Xử lý khi hoàn thành
        console.log('Selected:', selector.selected);
    }
});
```

### ThenSelector - Chọn tuần tự

Cho phép chọn các phần tử theo thứ tự.

#### Cú pháp:
```typescript
import { then, vertex, stroke } from '../selectors';

const lineFromPointsSelector = then([
    vertex({ preview: true }),  // Chọn điểm đầu
    vertex({ preview: true })   // Chọn điểm cuối
], {
    onComplete: (selector, doc) => {
        const [point1, point2] = selector.selected;
        createLine(point1, point2);
    }
});
```

### RepeatSelector - Chọn lặp lại

Cho phép chọn cùng một loại phần tử nhiều lần.

#### Cú pháp:
```typescript
import { repeat, vertex } from '../selectors';

const polygonSelector = repeat(vertex({ preview: true }), {
    count: 4,  // Chọn đúng 4 điểm
    onPartialSelection: (newSel, curSel, selector, doc) => {
        // Xử lý mỗi lần chọn
        console.log(`Đã chọn ${curSel.length} điểm`);
        return true;  // Tiếp tục chọn
    },
    onComplete: (selector, doc) => {
        // Hoàn thành chọn 4 điểm
        createQuadrilateral(selector.selected);
    }
});
```

## Common Selection Utilities

### vertexS() - Selector điểm thông dụng

Tạo OrSelector cho việc chọn điểm (vertex hoặc vertex on stroke).

```typescript
import { vertexS } from '../selectors';

const commonVertexSelector = vertexS(previewQueue, cursor);
// Tương đương với:
// or([vertex(...), vertexOnStroke(...)], { flatten: true })
```

### strokeS() - Selector đường thông dụng

Tạo OrSelector cho việc chọn stroke.

```typescript
import { strokeS } from '../selectors';

const commonStrokeSelector = strokeS(previewQueue, cursor);
```

### nPoints() - Chọn n điểm với exclusion

Chọn n điểm với tự động loại trừ điểm đã chọn.

```typescript
import { nPoints } from '../selectors';

const trianglePoints = nPoints(previewQueue, cursor, {
    count: 3,
    onPartialSelection: (newSel, curSel, selector, doc) => {
        // Xử lý mỗi điểm được chọn
        return true;
    }
});
```

### triangleWithProj() - Logic chọn tam giác với ràng buộc

Chọn 2 điểm đầu tự do, điểm thứ 3 có ràng buộc.

```typescript
import { triangleWithProj, perpLinesTransform, perpLinesCheck } from '../selectors';

const rightTriangleSelector = triangleWithProj(
    previewQueue,
    cursor,
    perpLinesTransform,  // Transform function
    perpLinesCheck       // Check function
);
```

## Ví dụ thực tế từ Tools

### Tool tạo đường thẳng
```typescript
export class CreateLineTool extends GeometryTool {
    selLogic: RepeatSelector<SelectedVertex>;
    
    constructor() {
        // Chọn 2 điểm để tạo đường thẳng
        this.selLogic = repeat(vertexS(this.pQ, this.cursor), {
            count: 2,
            onComplete: this.createLine.bind(this)
        });
    }
    
    handlePointerEvent(event: GeoPointerEvent) {
        const selected = this.selLogic.trySelect(event, docCtrl);
        // Xử lý preview line nếu có 2 điểm
        if (selected && selected.length === 2) {
            this.showPreviewLine(selected[0], selected[1]);
        }
    }
}
```

### Tool tạo góc
```typescript
export class CreateAngleTool extends GeometryTool {
    selLogic: ThenSelector;
    
    createSelLogic() {
        // Chọn 2 đường thẳng trước
        const lineSelector = repeat(stroke({
            selectableStrokeTypes: ['RenderLine']
        }), { count: 2 });
        
        // Sau đó chọn điểm chỉ hướng
        const directionSelector = vertex({
            refinedFilter: (vertex) => {
                // Loại trừ các điểm đầu/cuối của đường đã chọn
                return !this.isEndpointOfSelectedLines(vertex);
            }
        });
        
        this.selLogic = then([lineSelector, directionSelector], {
            onComplete: this.createAngle.bind(this)
        });
    }
}
```

### Tool tạo hình chữ nhật
```typescript
export class CreateRectangleTool extends GeometryTool {
    createSelLogic() {
        // Sử dụng triangleWithProj để tạo tam giác vuông
        const first3Points = triangleWithProj(
            this.pQ,
            this.cursor,
            perpLinesTransform,  // Ràng buộc góc vuông
            perpLinesCheck
        );
        
        // Điểm thứ 4 được suy ra tự động
        this.selLogic = then([first3Points, this.lastPointSelector()], {
            onComplete: this.createRectangle.bind(this)
        });
    }
}
```

## Best Practices

### 1. Quản lý Preview Queue
```typescript
// Luôn tạo PreviewQueue cho tool
pQ = new PreviewQueue();

// Truyền vào tất cả selectors
const selector = vertex({ previewQueue: this.pQ });
```

### 2. Sử dụng Cursor Management
```typescript
// Truyền cursor để quản lý con trỏ chuột
const selector = vertex({ 
    cursor: this.pointerHandler.cursor 
});
```

### 3. Reset Selector đúng cách
```typescript
override resetState() {
    this.selLogic.reset();  // Reset selector
    super.resetState();
}
```

### 4. Xử lý Exclusion trong Repeat
```typescript
// Sử dụng refinedFilter để loại trừ elements đã chọn
const selector = vertex({
    refinedFilter: (el) => !this.selectedElements.includes(el)
});
```

### 5. Combine Selectors hiệu quả
```typescript
// Sử dụng common utilities thay vì tự tạo
const pointSelector = vertexS(pQ, cursor);  // Tốt hơn
// thay vì tự tạo or([vertex(...), vertexOnStroke(...)])
```

### 6. Xử lý Transform Functions
```typescript
const selector = vertex({
    tfunc: (previewEl, doc) => {
        // Luôn return một RenderVertex hợp lệ
        // Modify previewEl in-place và return nó
        previewEl.coords[0] = constrainedX;
        previewEl.coords[1] = constrainedY;
        return previewEl;
    }
});
```

### 7. Error Handling
```typescript
// Luôn kiểm tra selected trước khi sử dụng
if (selector.selected && selector.selected.length > 0) {
    // Xử lý selection
}
```

Selector System cung cấp một framework mạnh mẽ và linh hoạt để xây dựng các tool geometry phức tạp với logic selection nhất quán và dễ bảo trì.
